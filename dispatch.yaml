# PropBolt Enterprise - App Engine Dispatch Configuration
# Routes domains to auto-scaling services with load balancing
# Priority: API > Dashboard > Landing

dispatch:
  # HIGH PRIORITY: API Service (api.propbolt.com)
  # Enterprise-grade auto-scaling and load balancing
  - url: "api.propbolt.com/*"
    service: api

  # MEDIUM PRIORITY: Dashboard (go.propbolt.com)
  # User and Admin portal with moderate scaling
  - url: "go.propbolt.com/*"
    service: dashboard

  # LOW PRIORITY: Landing Page (propbolt.com)
  # Marketing site with basic scaling
  - url: "propbolt.com/*"
    service: default

  # WWW redirect to main domain
  - url: "www.propbolt.com/*"
    service: default

  # Default fallback for any unmatched domains
  - url: "*/*"
    service: default
